import os
import re
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from datetime import datetime
import time
import logging
from pathlib import Path
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("email_sender.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

def extract_email_and_name(filename):
    try:
        # Extract the ID and name
        match = re.match(r'^(\d+)_(.+)\..*$', filename)
        if match:
            email_id = f"{match.group(1)}@klu.ac.in"
            name = match.group(2).strip()
            return email_id, name
        else:
            logger.error(f"Couldn't parse filename: {filename}")
            return None, None
    except Exception as e:
        logger.error(f"Error extracting email from {filename}: {str(e)}")
        return None, None
def create_html_email(recipient_name):
    
    # Current date for the email
    current_date = datetime.now().strftime("%B %d, %Y")
    
    # HTML Email Template
    html = """
    <html>
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="font-family:sans-serif;max-width:700px;margin:auto;border:1px solid #ddd;border-radius:10px;overflow:hidden">
    <tbody>
        <!-- Header -->
        <tr>
            <td align="center" bgcolor="#34A853" style="padding:20px;color:#ffffff;font-size:24px;font-weight:bold">
                📢 You're In! <span style="color:#fbbc05">Repo Ready</span> Workshop Confirmed!
            </td>
        </tr>

        <!-- Body -->
        <tr>
            <td style="padding:20px;color:#333">
                <p style="font-size:18px;font-weight:bold;text-align:center">🔧 Version Control Like a Pro with <strong>Repo Ready</strong></p>
                <p>We’re thrilled to confirm your spot in the <strong>Repo Ready, Introduction to  Git & GitHub Workshop</strong> organized by <strong>GDG on Campus KARE</strong>.</p>
                
                <h3 style="color:#4285F4">📍 Venue & Schedule</h3>
                <ul style="line-height:1.7">
                    <li><strong>Venue:</strong> 8301A and 8301B</li>
                    <li><strong>Date:</strong> 5th, 6th & 7th August 2025</li>
                    <li><strong>Time:</strong> 5:00 PM to 6:00 PM</li>
                    <li><strong>Note:</strong> Please make sure to reach the venue at least <strong>5 minutes early</strong>.</li>
                </ul>

                <h3 style="color:#EA4335">💻 What You Need</h3>
                <ul style="line-height:1.7">
                    <li>Laptop (fully charged)</li>
                    <li><a href="https://git-scm.com/downloads" target="_blank">Git installed</a></li>
                    <li><a href="https://code.visualstudio.com/download" target="_blank">VS Code installed</a></li>
                    <li><a href="https://www.youtube.com/watch?v=HCqIdfNfjWk"> Github Account creation Intstructions</a></li>
                    <li>WhatsApp should be logged in (desktop/browser)</li>
                </ul>

                <p style="font-weight:bold;color:#666;text-align:center">✅ We’ll guide you through everything else during the sessions!</p>

                <p style="text-align:center;font-weight:bold;color:#34A853">Let’s get <span style="color:#ea4335">Repo Ready</span> together and master Git the fun way! 🚀</p>

                <!-- Buttons -->
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="text-align:center;margin-top:20px">
                    <tr>
                        <td>
                            <a href="https://gdg-kare.tech/git-workshop/" style="display:inline-block;background-color:#4285F4;color:white;text-decoration:none;padding:10px 20px;border-radius:5px;font-weight:bold;margin:5px" target="_blank">Event Page</a>
                        </td>
                        <td>
                            <a href="https://chat.whatsapp.com/LSTyx32XRYC7zWb0TD8eDZ?mode=ac_t" style="display:inline-block;background-color:#fbbc05;color:white;text-decoration:none;padding:10px 20px;border-radius:5px;font-weight:bold;margin:5px" target="_blank">Join WhatsApp Group</a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td align="center" bgcolor="#333" style="padding:15px;color:white;font-size:12px">
                Kalasalingam University, Anand Nagar, Krishnankoil, Srivilliputhur - 626126
            </td>
        </tr>
    </tbody>
</table>
</html>
    """
    return html

def send_email(smtp_server, port, sender_email, password, recipient_email, subject, 
               html_content, certificate_path, recipient_name, logo_path=None):
    """
    Send an email with the certificate attached and GDG logo embedded in the HTML.
    """
    try:
        message = MIMEMultipart('related')
        message['Subject'] = subject
        message['From'] = sender_email
        message['To'] = recipient_email
        
        # Attach HTML content
        html_part = MIMEText(html_content, 'html')
        message.attach(html_part)
        
        # Attach the certificate image
        with open(certificate_path, 'rb') as img_file:
            certificate_img = MIMEImage(img_file.read())
            certificate_img.add_header('Content-ID', '<certificate>')
            certificate_img.add_header('Content-Disposition', 'inline', 
                                    filename=os.path.basename(certificate_path))
            message.attach(certificate_img)
        
        # Attach logo image if provided
        if logo_path:
            try:
                with open(logo_path, 'rb') as logo_file:
                    logo_img = MIMEImage(logo_file.read())
                    logo_img.add_header('Content-ID', '<logo1>')
                    logo_img.add_header('Content-Disposition', 'inline', 
                                     filename=os.path.basename(logo_path))
                    message.attach(logo_img)
            except FileNotFoundError:
                logger.warning(f"Logo file not found: {logo_path}")
        
        # Connect to the SMTP server and send the email
        with smtplib.SMTP(smtp_server, port) as server:
            server.starttls()
            server.login(sender_email, password)
            server.send_message(message)
            logger.info(f"Email sent successfully to {recipient_email} ({recipient_name})")
            return True
            
    except Exception as e:
        logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
        return False

def process_certificates(certificates_dir, logo_paths, smtp_settings, dry_run=False):
    """
    Process all certificate images in the specified directory and send emails.
    """
    cert_dir = Path(certificates_dir)
    
    if not cert_dir.exists() or not cert_dir.is_dir():
        logger.error(f"Certificate directory not found: {certificates_dir}")
        return
    
    # Get list of image files
    image_extensions = ['.png', '.jpg', '.jpeg', '.gif']
    certificate_files = [f for f in os.listdir(cert_dir) 
                        if any(f.lower().endswith(ext) for ext in image_extensions)]
    
    if not certificate_files:
        logger.warning(f"No certificate images found in {certificates_dir}")
        return
    
    logger.info(f"Found {len(certificate_files)} certificate images to process")
    
    success_count = 0
    failed_count = 0
    
    for cert_file in certificate_files:
        email_id, name = extract_email_and_name(cert_file)
        
        if not email_id or not name:
            logger.error(f"Skipping {cert_file}: Could not extract email or name")
            failed_count += 1
            continue
        
        cert_path = cert_dir / cert_file
        html_content = create_html_email(name)
        subject = f"Thank You for Participating in G2HackFest - Certificate of Achievement"
        
        logger.info(f"Processing: {cert_file} -> {email_id} ({name})")
        
        if dry_run:
            logger.info(f"[DRY RUN] Would send email to {email_id} with certificate {cert_file}")
            success_count += 1
        else:
            success = send_email(
                smtp_settings['server'],
                smtp_settings['port'],
                smtp_settings['email'],
                smtp_settings['password'],
                email_id,
                subject,
                html_content,
                cert_path,
                name,
            )
            
            if success:
                success_count += 1
            else:
                failed_count += 1
            
            # Sleep briefly to avoid overwhelming the SMTP server
            time.sleep(2)
    
    logger.info(f"Email sending complete. Success: {success_count}, Failed: {failed_count}")
def main():
    parser = argparse.ArgumentParser(description='Send thank you emails with certificates to G2HackFest participants')
    parser.add_argument('--certificates', '-c', required=True, help='Directory containing certificate images')
    parser.add_argument('--email', '-e', required=True, help='Sender email address')
    parser.add_argument('--password', '-p', required=True, help='Email password')
    parser.add_argument('--server', '-s', default='smtp.gmail.com', help='SMTP server address')
    parser.add_argument('--port', '-pt', type=int, default=587, help='SMTP server port')
    parser.add_argument('--dry-run', '-d', action='store_true', help='Perform a dry run without sending emails')
    
    args = parser.parse_args()
    
    smtp_settings = {
        'server': args.server,
        'port': args.port,
        'email': args.email,
        'password': args.password
    }
    
    process_certificates(args.certificates, args.dry_run, smtp_settings)

if __name__ == "__main__":
    main()